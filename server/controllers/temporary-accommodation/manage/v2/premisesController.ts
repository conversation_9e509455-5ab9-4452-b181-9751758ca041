import type { Request, RequestHandler, Response } from 'express'

import { PremisesSearchParameters } from '@approved-premises/ui'
import type { Cas3PremisesStatus } from '../../../../@types/shared'
import PremisesService from '../../../../services/v2/premisesService'
import extractCallConfig from '../../../../utils/restUtils'

export default class PremisesController {
  constructor(private readonly premisesService: PremisesService) {}

  index(): RequestHandler {
    return async (req: Request, res: Response) => {
      const callConfig = extractCallConfig(req)

      const params = req.query as PremisesSearchParameters & { status?: string }

      // If no status parameter, redirect to include status=Online
      if (!params.status) {
        const queryString = new URLSearchParams({ ...params, status: 'Online' }).toString()
        return res.redirect(`${req.path}?${queryString}`)
      }

      // Convert capitalized status to lowercase for API
      const apiStatus: Cas3PremisesStatus = params.status.toLowerCase() as Cas3PremisesStatus

      const searchData = await this.premisesService.searchData(callConfig, params, apiStatus)

      return res.render('temporary-accommodation/v2/premises/index', {
        ...searchData,
        params: { ...params, status: params.status },
        status: apiStatus,
        isOnlineTab: apiStatus === 'online',
        isArchivedTab: apiStatus === 'archived'
      })
    }
  }
}
